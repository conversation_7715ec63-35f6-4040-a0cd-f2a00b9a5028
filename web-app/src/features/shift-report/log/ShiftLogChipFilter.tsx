import React, { useState, useEffect } from 'react';
import { Chip, Stack, IconButton, Popover, FormControlLabel, Tooltip, Typography, Checkbox } from '@mui/material';
import TuneIcon from '@mui/icons-material/Tune';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import { useAppDispatch, useAppSelector } from '../../../store';
import { setShiftLogViewState } from './shiftLogSlice';
import { User } from '../../user/userTypes';
import {
  ShiftLogColumn,
  ShiftLogColumnDefaults,
  ShiftLogColumnDisplayMap,
  ShiftLogColumnSetting,
} from './shiftLogTypes';

interface ShiftLogChipFilterProps {
  me?: User;
  resetPageNumber: () => void;
}

function ShiftLogChipFilter({ me, resetPageNumber }: ShiftLogChipFilterProps) {
  const shiftLogViewState = useAppSelector((state) => state.shiftLog.shiftLogViewState);
  const dispatch = useAppDispatch();

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [columnsOrder, setColumnsOrder] = useState<ShiftLogColumnSetting[]>(
    shiftLogViewState.columns || ShiftLogColumnDefaults
  );
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);

  useEffect(() => {
    setColumnsOrder(shiftLogViewState.columns || ShiftLogColumnDefaults);
  }, [shiftLogViewState.columns]);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? 'column-popover' : undefined;

  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (index: number) => {
    if (draggingIndex !== null && draggingIndex !== index) {
      const newOrder = [...columnsOrder];
      const [moved] = newOrder.splice(draggingIndex, 1);
      newOrder.splice(index, 0, moved);
      setColumnsOrder(newOrder);
      // Update view state with the new ordering.
      dispatch(setShiftLogViewState({ ...shiftLogViewState, columns: newOrder }));
    }
    setDraggingIndex(null);
  };

  const handleToggleColumn = (column: ShiftLogColumn) => {
    // Toggle the hidden flag for the selected column.
    const newOrder = columnsOrder.map((setting) => {
      if (setting.column === column) {
        return { ...setting, hidden: !setting.hidden };
      }
      return setting;
    });
    setColumnsOrder(newOrder);
    dispatch(setShiftLogViewState({ ...shiftLogViewState, columns: newOrder }));
  };

  return (
    <Stack direction="row" flexWrap={{ xs: 'nowrap', sm: 'wrap' }} overflow={{ xs: 'scroll', sm: 'unset' }}>
      <Chip
        label="Created by me"
        sx={{ mb: 1, mr: 1 }}
        color={shiftLogViewState.createdBy ? 'primary' : 'default'}
        onClick={() => {
          const newState = { ...shiftLogViewState };
          if (newState.createdBy) {
            newState.createdBy = undefined;
          } else {
            newState.createdBy = me;
          }
          dispatch(setShiftLogViewState(newState));
          resetPageNumber();
        }}
      />
      <Tooltip title="View options">
        <IconButton size="small" sx={{ p: 0.5, mb: 1, mr: 1 }} onClick={handleClick}>
          <TuneIcon fontSize="small" />
        </IconButton>
      </Tooltip>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Columns</Typography>
          <Stack>
            {columnsOrder.map((setting, index) => (
              <div
                key={setting.column}
                draggable
                onDragStart={() => handleDragStart(index)}
                onDragOver={handleDragOver}
                onDrop={() => handleDrop(index)}
                style={{ display: 'flex', alignItems: 'center', cursor: 'grab' }}
              >
                <DragIndicatorIcon fontSize="small" style={{ marginRight: 8 }} />
                <FormControlLabel
                  control={<Checkbox checked={!setting.hidden} onChange={() => handleToggleColumn(setting.column)} />}
                  label={ShiftLogColumnDisplayMap[setting.column]}
                />
              </div>
            ))}
          </Stack>
        </Stack>
      </Popover>
    </Stack>
  );
}

export default ShiftLogChipFilter;
