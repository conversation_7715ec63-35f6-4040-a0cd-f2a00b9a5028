import { useEffect, useState, useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { TabContext, TabPanel } from '@mui/lab';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { FlagOutlined } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '../../../store';
import {
  ShiftLogParams,
  ShiftLogRead,
  ShiftLogColumn,
  ShiftLogColumnDefaults,
  ShiftLogColumnDisplayMap,
  ShiftLogFieldSortMap,
  ShiftLogSort,
  ShiftLogSortField,
} from './shiftLogTypes';
import { useGetShiftLogsQuery } from './shiftLogApi';
import { setShiftLogViewState } from './shiftLogSlice';
import ErrorGate from '../../../components/ErrorGate';
import PageTitle from '../../title/Title';
import ShiftLogFilterBar from './ShiftLogFilterBar';
import ShiftLogChipFilter from './ShiftLogChipFilter';
import { ROUTES } from '../../../constants';
import { DataGridCellLinkWrapper } from '../../../components/DataGridCellLink';
import usePaging from '../../../components/hooks/usePaging';
import NoRowsOverlay from '../../../components/NoRowsOverlay';
import { useGetCurrentUserQuery } from '../../user/userApi';
import SidCell from './cell/SidCell';
import ShiftLogDescriptionCell from './cell/ShiftLogDescriptionCell';
import ShiftLogReferenceNumberCell from './cell/ShiftLogReferenceNumberCell';
import ShiftLogTopicCell from './cell/ShiftLogTopicCell';
import ShiftLogGroupCell from './cell/ShiftLogGroupCell';
import ShiftLogUserCell from './cell/ShiftLogUserCell';
import ShiftLogDateCell from './cell/ShiftLogDateCell';
import ShiftLogPriorityCell from './cell/ShiftLogPriorityCell';

const getShiftLogUrl = (shiftReportId: number) => `./..${ROUTES.SHIFT_REPORTS}/${shiftReportId}`;

const getGridModelFromSort = (sort: ShiftLogSort[]): GridSortModel =>
  sort.map((s) => ({
    field:
      Object.keys(ShiftLogFieldSortMap).find((key) => ShiftLogFieldSortMap[key as keyof ShiftLogRead] === s.field) || s.field,
    sort: s.direction,
  }));

const getSortFromGridModel = (gridModel: GridSortModel): ShiftLogSort[] =>
  gridModel.map((model) => ({
    field: ShiftLogFieldSortMap[model.field as keyof ShiftLogRead] || (model.field as ShiftLogSortField),
    direction: model.sort as 'asc' | 'desc',
  }));

const columnDefaults: Record<ShiftLogColumn, GridColDef<ShiftLogRead>> = {
  [ShiftLogColumn.PRIORITY]: {
    field: ShiftLogColumn.PRIORITY,
    renderHeader: () => (
      <FlagOutlined
        sx={{ mx: 1, fontSize: 16, color: 'rgba(0, 0, 0, 0.85)' }}
        titleAccess={ShiftLogColumnDisplayMap[ShiftLogColumn.PRIORITY]}
        color="action"
      />
    ),
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.PRIORITY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogPriorityCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.SID]: {
    field: ShiftLogColumn.SID,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(SidCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.DESCRIPTION]: {
    field: ShiftLogColumn.DESCRIPTION,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.DESCRIPTION],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogDescriptionCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.REFERENCE_NUMBER]: {
    field: ShiftLogColumn.REFERENCE_NUMBER,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.REFERENCE_NUMBER],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogReferenceNumberCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.TOPIC]: {
    field: ShiftLogColumn.TOPIC,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.TOPIC],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogTopicCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.GROUP]: {
    field: ShiftLogColumn.GROUP,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.GROUP],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogGroupCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.CREATED_BY]: {
    field: ShiftLogColumn.CREATED_BY,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.CREATED_BY],
    sortable: false,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogUserCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
  [ShiftLogColumn.DATE]: {
    field: ShiftLogColumn.DATE,
    headerName: ShiftLogColumnDisplayMap[ShiftLogColumn.DATE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftLogRead>) =>
      DataGridCellLinkWrapper(ShiftLogDateCell(params), getShiftLogUrl(params.row.shiftReport.id)),
  },
};

function ShiftLogListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const shiftLogViewState = useAppSelector((state) => state.shiftLog.shiftLogViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(shiftLogViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = shiftLogViewState.columns ? shiftLogViewState.columns : ShiftLogColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [shiftLogViewState.columns]);

  const [queryParams, setQueryParams] = useState<ShiftLogParams>({
    ancestorGroup: Number(groupId),
    pageSize,
    pageNumber: page,
  });

  const { data, isLoading, error } = useGetShiftLogsQuery(queryParams);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      pageSize,
      pageNumber: page,
    }));
  }, [page, pageSize]);

  useEffect(() => {
    if (shiftLogViewState) {
      setQueryParams((prev) => ({
        ...prev,
        group: shiftLogViewState.group?.id,
        topic: shiftLogViewState?.topic?.id,
        subTopic: shiftLogViewState?.subTopic?.id,
        search: shiftLogViewState?.search,
        priority: shiftLogViewState?.priority,
        createDateGte: shiftLogViewState?.createDateGte,
        createDateLte: shiftLogViewState?.createDateLte,
      }));
    }
  }, [shiftLogViewState]);

  useEffect(() => {
    if (shiftLogViewState.sort && shiftLogViewState.sort.length > 0) {
      const sort = shiftLogViewState.sort[0];
      const sortString = `${sort.field}:${sort.direction}`;
      setQueryParams((prev) => ({
        ...prev,
        sort: sortString,
      }));
    } else {
      setQueryParams((prev) => ({
        ...prev,
        sort: undefined,
      }));
    }
  }, [shiftLogViewState.sort]);

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setShiftLogViewState({
        ...shiftLogViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Shift logs" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={shiftLogViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="All logs" value="all" />
          </Tabs>
        </Box>
        <Box display="flex" flexDirection="column" gap={1}>
          <ShiftLogChipFilter me={me} resetPageNumber={resetPageNumber} />
          <ShiftLogFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        </Box>
        <TabPanel
          sx={{
            px: 0,
            pt: 1,
            pb: 0,
          }}
          value="0"
        >
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                slotProps={{
                  noRowsOverlay: {
                    title: 'No shift logs found',
                  },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}

export default ShiftLogListPage;
