import { themeToColor } from '../../../theme';
import { EnumMetaItem, EnumMetaMap } from '../../../types';
import { createDynamicMetaMap, getWeekNumberAndYear, sortDates } from '../../../utils';
import { GroupDisplay } from '../../group/groupTypes';
import { User, UserDisplay } from '../../user/userTypes';

export interface ShiftLog {
  id?: number;
  sid?: number;
  description: string;
  referenceNumber?: string;
  priority: ShiftLogPriority;
  creationDate?: number;
  createdBy?: UserDisplay;
}

export enum ShiftLogPriority {
  HIGH = 'HIGH',
  LOW = 'LOW',
}

export const ShiftLogPriorityDisplayMap: Record<ShiftLogPriority, string> = {
  HIGH: 'High',
  LOW: 'Low',
};

export const ShiftLogPriorityIconMap: Record<ShiftLogPriority, string> = {
  [ShiftLogPriority.HIGH]: 'error.main',
  [ShiftLogPriority.LOW]: 'action.disabled',
};

export interface ShiftLogViewState {
  listView: 'all';
  group?: GroupDisplay;
  topic?: TopicDisplay;
  subTopic?: SubTopicDisplay;
  priority?: ShiftLogPriority;
  createdBy?: User;
  createDateGte?: number;
  createDateLte?: number;
  search?: string;
  sort?: ShiftLogSort[];
  columns?: ShiftLogColumnSetting[];
}
export interface ShiftLogState {
  shiftLogViewState: ShiftLogViewState;
}

export interface ShiftLogParams {
  shiftLogSubTopic?: number;
  group?: number;
  ancestorGroup?: number;
  priority?: ShiftLogPriority;
  search?: string;
  topic?: number;
  subTopic?: number;
  createDateGte?: number;
  createDateLte?: number;
  pageNumber?: number;
  pageSize?: number;
  sort?: string;
}

export interface ShiftLogCreate {
  shiftLogSubTopic: number;
  description: string;
  priority: ShiftLogPriority;
}

export interface ShiftLogUpdate {
  id: number;
  description: string;
  referenceNumber?: string;
  priority?: ShiftLogPriority;
}

export interface SubTopicDisplay {
  id: number;
  name: string;
}

export interface ShiftReportDisplay {
  id: number;
  name: string;
  group: GroupDisplay;
}

export interface TopicDisplay {
  id: number;
  name: string;
}
export interface ShiftLogRead {
  id?: number;
  sid?: number;
  shiftReportSubTopic?: number;
  description: string;
  referenceNumber?: string;
  priority: ShiftLogPriority;
  date: number;
  topic: TopicDisplay;
  subTopic: SubTopicDisplay;
  shiftReport: ShiftReportDisplay;
  creationDate: number;
  createdBy: UserDisplay;
}

export interface ShiftLogDeletable {
  deletable: boolean;
}

export enum ShiftLogGroupBy {
  GROUP = 'SHIFT_REPORT_LOGS_GROUP',
  REPORTED_BY = 'SHIFT_REPORT_LOGS_REPORTED_BY',
  SUBTOPIC = 'SHIFT_REPORT_LOGS_SUBTOPIC',
  TOPIC = 'SHIFT_REPORT_LOGS_TOPIC',
  CREATION_DATE_WEEK = 'SHIFT_REPORT_LOGS_CREATION_DATE_WEEK',
}

export const ShiftLogGroupByDisplayMap: Record<ShiftLogGroupBy, string> = {
  [ShiftLogGroupBy.GROUP]: 'Group',
  [ShiftLogGroupBy.REPORTED_BY]: 'User',
  [ShiftLogGroupBy.SUBTOPIC]: 'Subtopic',
  [ShiftLogGroupBy.TOPIC]: 'Topic',
  [ShiftLogGroupBy.CREATION_DATE_WEEK]: 'Shift log week',
};

export const CreationDateWeekMeta = createDynamicMetaMap<string, EnumMetaItem>((iso) => ({
  label: getWeekNumberAndYear(iso),
  color: themeToColor('primary.main'),
}));

export interface ShiftLogGroupByFieldType {
  [ShiftLogGroupBy.CREATION_DATE_WEEK]: string;
}

export const ShiftLogGroupByFieldMetaMap: {
  [K in keyof ShiftLogGroupByFieldType]: EnumMetaMap<ShiftLogGroupByFieldType[K]>;
} = {
  [ShiftLogGroupBy.CREATION_DATE_WEEK]: CreationDateWeekMeta,
};

export const ShiftLogGroupByFieldSortFunctionMap = {
  [ShiftLogGroupBy.CREATION_DATE_WEEK]: sortDates,
};

export enum ShiftLogSortField {
  SID = 'SID',
  DESCRIPTION = 'DESCRIPTION',
  REFERENCE_NUMBER = 'REFERENCE_NUMBER',
  PRIORITY = 'PRIORITY',
  DATE = 'DATE',
  CREATED_BY = 'CREATED_BY',
}

export const ShiftLogFieldSortMap: Partial<Record<keyof ShiftLogRead, ShiftLogSortField>> = {
  sid: ShiftLogSortField.SID,
  description: ShiftLogSortField.DESCRIPTION,
  referenceNumber: ShiftLogSortField.REFERENCE_NUMBER,
  priority: ShiftLogSortField.PRIORITY,
  date: ShiftLogSortField.DATE,
};

export interface ShiftLogSort {
  field: ShiftLogSortField;
  direction: 'asc' | 'desc';
}

export enum ShiftLogColumn {
  SID = 'sid',
  DESCRIPTION = 'description',
  REFERENCE_NUMBER = 'referenceNumber',
  TOPIC = 'topic',
  GROUP = 'group',
  CREATED_BY = 'createdBy',
  DATE = 'date',
  PRIORITY = 'priority',
}

export const ShiftLogColumnDisplayMap: Record<ShiftLogColumn, string> = {
  [ShiftLogColumn.SID]: 'ID',
  [ShiftLogColumn.DESCRIPTION]: 'Description',
  [ShiftLogColumn.REFERENCE_NUMBER]: 'Ref nr.',
  [ShiftLogColumn.TOPIC]: 'Topic',
  [ShiftLogColumn.GROUP]: 'Reported by group',
  [ShiftLogColumn.CREATED_BY]: 'Reported by',
  [ShiftLogColumn.DATE]: 'Date',
  [ShiftLogColumn.PRIORITY]: 'Priority',
};

export interface ShiftLogColumnSetting {
  column: ShiftLogColumn;
  hidden: boolean;
  width: number;
}

export const ShiftLogColumnDefaults: ShiftLogColumnSetting[] = [
  {
    column: ShiftLogColumn.PRIORITY,
    hidden: false,
    width: 30,
  },
  {
    column: ShiftLogColumn.SID,
    hidden: false,
    width: 75,
  },
  {
    column: ShiftLogColumn.DESCRIPTION,
    hidden: false,
    width: 400,
  },
  {
    column: ShiftLogColumn.REFERENCE_NUMBER,
    hidden: false,
    width: 100,
  },
  {
    column: ShiftLogColumn.TOPIC,
    hidden: false,
    width: 150,
  },
  {
    column: ShiftLogColumn.GROUP,
    hidden: false,
    width: 200,
  },
  {
    column: ShiftLogColumn.CREATED_BY,
    hidden: false,
    width: 200,
  },
  {
    column: ShiftLogColumn.DATE,
    hidden: false,
    width: 175,
  },
];
